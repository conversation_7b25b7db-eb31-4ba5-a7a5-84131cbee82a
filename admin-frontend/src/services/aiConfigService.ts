import { request } from './api';
import {
  AIServiceConfig,
  AIServiceModel,
  CreateConfigRequest,
  UpdateConfigRequest,
  ConfigListResponse,
  ConfigResponse,
  ModelListResponse,
  TestResponse,
  UsageStatsResponse,
  ActiveAIConfig,
  ActiveConfigStats,
  ActivateConfigRequest,
  ActiveConfigResponse,
  ActiveConfigStatsResponse,
  ApiResponse,
  LocalModelsApiResponse,
  ManualModelRequest,
  BatchManualModelRequest,
  ManualModelResponse
} from '../types/aiConfig';

const API_BASE = '/admin/ai';

// 工具函数：判断是否为本地地址
const isLocalAddress = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname.toLowerCase();
    return hostname === 'localhost' || 
           hostname === '127.0.0.1' || 
           hostname === '::1' ||
           hostname.startsWith('192.168.') ||
           hostname.startsWith('10.') ||
           hostname.startsWith('172.16.') ||
           hostname.endsWith('.local');
  } catch {
    return false;
  }
};

// 获取AI配置列表
export const getConfigs = async (params?: {
  page?: number;
  limit?: number;
  search?: string;
  provider?: string;
  isActive?: boolean;
}): Promise<ConfigListResponse> => {
  const response = await request.get(`${API_BASE}/configs`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取配置列表失败');
  }
  return response as ConfigListResponse;
};

// 获取单个AI配置
export const getConfig = async (id: string): Promise<ConfigResponse> => {
  const response = await request.get(`${API_BASE}/configs/${id}`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取配置失败');
  }
  return response as ConfigResponse;
};

// 创建AI配置
export const createConfig = async (data: CreateConfigRequest): Promise<ConfigResponse> => {
  const response = await request.post(`${API_BASE}/configs`, data);
  if (!response.success || !response.data) {
    throw new Error(response.message || '创建配置失败');
  }
  return response as ConfigResponse;
};

// 更新AI配置
export const updateConfig = async (id: string, data: UpdateConfigRequest): Promise<ConfigResponse> => {
  const response = await request.put(`${API_BASE}/configs/${id}`, data);
  if (!response.success || !response.data) {
    throw new Error(response.message || '更新配置失败');
  }
  return response as ConfigResponse;
};

// 删除AI配置
export const deleteConfig = async (id: string): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/configs/${id}`);
};

// 测试AI配置连接
export const testConfig = async (id: string): Promise<TestResponse> => {
  const response = await request.post(`${API_BASE}/configs/${id}/test`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '测试配置失败');
  }
  return response as TestResponse;
};

// 设置默认配置
export const setDefaultConfig = async (id: string): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${id}/set-default`);
};

// 获取本地模型列表（支持可选鉴权）
const fetchLocalModels = async (baseURL: string, authToken?: string): Promise<string[]> => {
  try {
    // 构建本地模型API的URL
    // 在开发环境中，使用相对路径通过Vite代理避免跨域
    // 在生产环境中，直接使用完整URL请求本地服务
    let requestUrl: string;

    if (isLocalAddress(baseURL)) {
      // 检查是否为生产环境
      const isProduction = import.meta.env.PROD;
      if (isProduction) {
        // 生产环境：直接使用完整URL请求本地服务
        requestUrl = new URL('/api/models', baseURL).toString();
      } else {
        // 开发环境：使用相对路径通过Vite代理
        requestUrl = '/api/models';
      }
    } else {
      // 非本地地址：直接使用完整URL
      requestUrl = new URL('/api/models', baseURL).toString();
    }

    const headers: Record<string, string> = { 'Accept': 'application/json' };
    if (authToken && authToken.trim()) {
      headers['Authorization'] = `Bearer ${authToken.trim()}`;
    }
    console.debug('[aiConfigService] fetchLocalModels: requestUrl=', requestUrl, 'authProvided=', Boolean(authToken), 'isProduction=', import.meta.env.PROD);
    const response = await fetch(requestUrl, { headers });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const raw: any = await response.json().catch(() => ({}));

    // 容错解析：兼容多种结构
    // 1) { success, message, data: { "1": [..], "10": [..] } }
    // 2) { data: string[] }
    // 3) { models: string[] }
    // 4) 直接就是 string[]
    const root = raw && typeof raw === 'object' && 'data' in raw ? raw.data : raw;

    const allModels: string[] = [];
    const pushIfString = (v: unknown) => { if (typeof v === 'string' && v.trim()) allModels.push(v.trim()); };

    if (Array.isArray(root)) {
      // 直接数组
      root.forEach(pushIfString);
    } else if (root && typeof root === 'object') {
      if (Array.isArray((root as any).models)) {
        // { models: [] }
        (root as any).models.forEach(pushIfString);
      } else {
        // { "1": [...], "10": [...], ... }
        Object.values(root as Record<string, unknown>).forEach((models) => {
          if (Array.isArray(models)) {
            (models as unknown[]).forEach(pushIfString);
          } else if (typeof models === 'string') {
            pushIfString(models);
          }
        });
      }
    }

    console.debug('[aiConfigService] fetchLocalModels: parsed models count=', allModels.length);
    if (allModels.length === 0) {
      throw new Error('未解析到模型列表');
    }

    // 去重并返回
    return Array.from(new Set(allModels));
  } catch (error) {
    console.error('获取本地模型列表失败:', error);
    throw new Error(`获取本地模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// 同步模型列表 - 统一处理逻辑
export const syncModels = async (
  configId: string,
  options?: { localToken?: string }
): Promise<ApiResponse<AIServiceModel[]>> => {
  try {
    console.log('开始同步模型列表，配置ID:', configId);

    // 统一使用后端同步接口，不再区分本地和远程
    const syncResponse = await request.post(
      `${API_BASE}/configs/${configId}/sync-models`,
      {}, // 空的请求体，让后端使用配置中保存的API密钥
      { timeout: 120000 }
    );

    if (!syncResponse.success) {
      throw new Error(syncResponse.message || '同步模型失败');
    }

    console.log('模型同步成功，返回模型数量:', syncResponse.data?.length || 0);
    return syncResponse;
  } catch (error) {
    console.error('同步模型失败:', error);
    throw error;
  }
};

// 获取模型列表
export const getModels = async (params?: {
  configId?: string;
  page?: number;
  limit?: number;
  search?: string;
}): Promise<ModelListResponse> => {
  const response = await request.get(`${API_BASE}/models`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取模型列表失败');
  }
  return response as ModelListResponse;
};

// 更新模型配置
export const updateModel = async (id: string, data: Partial<AIServiceModel>): Promise<ApiResponse<AIServiceModel>> => {
  return await request.put(`${API_BASE}/models/${id}`, data);
};

// 获取使用统计
export const getUsageStats = async (
  configId: string,
  params?: {
    startDate?: string;
    endDate?: string;
  }
): Promise<UsageStatsResponse> => {
  const response = await request.get(`${API_BASE}/configs/${configId}/usage-stats`, { params });
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取使用统计失败');
  }
  return response as UsageStatsResponse;
};

// 启用AI配置
export const activateConfig = async (configId: string, data: ActivateConfigRequest): Promise<ApiResponse> => {
  return await request.post(`${API_BASE}/configs/${configId}/activate`, data);
};

// 获取当前启用的配置
export const getActiveConfig = async (): Promise<ActiveConfigResponse> => {
  const response = await request.get(`${API_BASE}/active-config`);
  if (!response.success) {
    throw new Error(response.message || '获取启用配置失败');
  }
  return response as ActiveConfigResponse;
};

// 禁用当前配置
export const deactivateConfig = async (): Promise<ApiResponse> => {
  return await request.delete(`${API_BASE}/active-config`);
};

// 获取配置的可用模型
export const getConfigModels = async (configId: string): Promise<ApiResponse<AIServiceModel[]>> => {
  return await request.get(`${API_BASE}/configs/${configId}/models`);
};

// 获取启用配置统计
export const getActiveConfigStats = async (): Promise<ActiveConfigStatsResponse> => {
  const response = await request.get(`${API_BASE}/active-config/stats`);
  if (!response.success || !response.data) {
    throw new Error(response.message || '获取启用配置统计失败');
  }
  return response as ActiveConfigStatsResponse;
};

// 手动添加模型
export const addManualModels = async (
  configId: string,
  models: ManualModelRequest[]
): Promise<ManualModelResponse> => {
  const response = await request.post(
    `${API_BASE}/configs/${configId}/models/manual`,
    { models }
  );
  if (!response.success) {
    throw new Error(response.message || '添加模型失败');
  }
  return response as ManualModelResponse;
};

// 删除手动添加的模型
export const deleteManualModel = async (
  configId: string,
  modelId: string
): Promise<ApiResponse> => {
  const response = await request.delete(
    `${API_BASE}/configs/${configId}/models/${modelId}/manual`
  );
  if (!response.success) {
    throw new Error(response.message || '删除模型失败');
  }
  return response;
};

// 导出所有服务
export const aiConfigService = {
  getConfigs,
  getConfig,
  createConfig,
  updateConfig,
  deleteConfig,
  testConfig,
  setDefaultConfig,
  syncModels,
  getModels,
  updateModel,
  getUsageStats,
  activateConfig,
  getActiveConfig,
  deactivateConfig,
  getConfigModels,
  getActiveConfigStats,
  addManualModels,
  deleteManualModel,
};

export default aiConfigService;
