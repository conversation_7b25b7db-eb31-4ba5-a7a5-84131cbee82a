/**
 * Google AI客户端工厂
 */

import { GoogleGenerativeAI, GenerativeModel } from '@google/generative-ai';
import { IAIClient, IAIClientFactory, IAIMessage, IAIStreamChunk, IAICompletionOptions, AIClientError } from '../interfaces/IAIClient';
import { MessageConverter } from '../utils/messageConverter';
import { logger } from '../../../utils/logger';
import { IAIServiceConfig } from '../../../models/AIServiceConfig';
import { decryptApiKey } from '../../../utils/encryption';

/**
 * Google AI客户端实现
 */
class GoogleAIClient implements IAIClient {
    public readonly provider = 'google';
    private model: GenerativeModel;

    constructor(
        public readonly configId: string,
        public readonly modelName: string,
        private genAI: GoogleGenerativeAI
    ) {
        this.model = genAI.getGenerativeModel({ model: modelName });
    }

    async *createChatCompletionStream(
        messages: IAIMessage[],
        options?: IAICompletionOptions
    ): AsyncIterable<IAIStreamChunk> {
        const isGemini25 = this.modelName.includes('2.5');
        const maxRetries = isGemini25 ? 5 : 3;
        const baseTimeout = isGemini25 ? 90000 : 60000; // Gemini 2.5 需要更长超时

        logger.info(`Google AI 开始流式聊天: ${this.modelName} (Gemini 2.5: ${isGemini25})`);

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                logger.debug(`Google AI 流式聊天尝试 ${attempt}/${maxRetries}: ${this.modelName}`);

                // 转换消息格式
                const { systemInstruction, contents } = MessageConverter.toGoogleAI(messages);

                // 配置生成参数 - Gemini 2.5 需要特殊配置
                const generationConfig: any = {
                    temperature: options?.temperature ?? (isGemini25 ? 0.9 : 0.7),
                    maxOutputTokens: options?.maxTokens ?? 2048,
                };

                // Gemini 2.5 特殊配置
                if (isGemini25) {
                    generationConfig.topP = 0.95;
                    generationConfig.topK = 40;
                    generationConfig.candidateCount = 1;
                }

                // 创建聊天会话
                const chatParams: any = {
                    generationConfig,
                    history: contents.slice(0, -1) // 除了最后一条消息
                };

                // 只有在有系统指令时才添加
                if (systemInstruction) {
                    chatParams.systemInstruction = systemInstruction.parts.map(part => part.text).join('\n\n');
                }

                const chat = this.model.startChat(chatParams);

                // 获取最后一条用户消息
                const lastMessage = contents[contents.length - 1];
                if (!lastMessage || lastMessage.role !== 'user') {
                    throw new AIClientError('最后一条消息必须是用户消息', this.provider);
                }

                const prompt = lastMessage.parts.map(part => part.text).join('\n');
                logger.debug(`发送消息到 ${this.modelName}, 内容长度: ${prompt.length}`);

                // 设置超时处理
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => {
                        reject(new Error(`Google AI 流式请求超时 (${baseTimeout}ms)`));
                    }, baseTimeout);
                });

                // 发送流式请求
                const resultPromise = chat.sendMessageStream(prompt);
                const result = await Promise.race([resultPromise, timeoutPromise]) as any;

                let totalTokens = 0;
                let chunkCount = 0;
                let hasContent = false;

                logger.debug(`开始处理 ${this.modelName} 流式响应`);

                for await (const chunk of result.stream) {
                    try {
                        chunkCount++;
                        const text = chunk.text();

                        if (text) {
                            hasContent = true;
                            totalTokens += text.length; // 简单估算

                            logger.debug(`${this.modelName} 流式块 ${chunkCount}: ${text.length} 字符`);

                            yield {
                                content: text,
                                done: false
                            };
                        }

                        // 检查是否有错误或完成信号
                        if (chunk.candidates && chunk.candidates[0]) {
                            const candidate = chunk.candidates[0];

                            // 检查安全过滤
                            if (candidate.finishReason === 'SAFETY') {
                                logger.warn(`${this.modelName} 内容被安全过滤器阻止`);
                                yield {
                                    content: '',
                                    done: true,
                                    error: '内容被安全过滤器阻止，请调整输入内容'
                                };
                                return;
                            }

                            // 检查其他完成原因
                            if (candidate.finishReason && candidate.finishReason !== 'STOP') {
                                logger.warn(`${this.modelName} 异常完成: ${candidate.finishReason}`);
                            }
                        }

                    } catch (chunkError: any) {
                        logger.error(`${this.modelName} 处理流式块错误:`, chunkError);

                        // 对于 Gemini 2.5，某些块错误可以忽略
                        if (isGemini25 && chunkError.message?.includes('text')) {
                            continue;
                        }

                        throw chunkError;
                    }
                }

                // 发送完成信号
                yield {
                    content: '',
                    done: true,
                    usage: {
                        totalTokens: totalTokens
                    }
                };

                logger.info(`${this.modelName} 流式聊天完成: ${totalTokens} tokens, ${chunkCount} 块, 有内容: ${hasContent}`);
                return; // 成功完成，退出重试循环

            } catch (error: any) {
                const isLastAttempt = attempt === maxRetries;
                const shouldRetry = this.shouldRetryError(error, isGemini25);

                logger.error(`${this.modelName} 流式聊天错误 (尝试 ${attempt}/${maxRetries}):`, {
                    message: error.message,
                    code: error.code,
                    status: error.status,
                    shouldRetry: shouldRetry && !isLastAttempt
                });

                if (isLastAttempt || !shouldRetry) {
                    // 最后一次尝试或不应重试的错误
                    const errorMessage = this.getErrorMessage(error, isGemini25);

                    yield {
                        content: '',
                        done: true,
                        error: errorMessage
                    };

                    throw new AIClientError(
                        `Google AI 聊天失败: ${errorMessage}`,
                        this.provider,
                        error.code,
                        error.status
                    );
                }

                // 等待后重试
                const waitTime = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
                logger.debug(`${this.modelName} 等待 ${waitTime}ms 后重试...`);
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
    }

    /**
     * 判断错误是否应该重试
     */
    private shouldRetryError(error: any, isGemini25: boolean): boolean {
        // 网络相关错误应该重试
        if (error.message?.includes('ECONNRESET') ||
            error.message?.includes('timeout') ||
            error.message?.includes('ENOTFOUND') ||
            error.code === 'ECONNRESET' ||
            error.code === 'ETIMEDOUT') {
            return true;
        }

        // 服务器错误应该重试
        if (error.status >= 500 && error.status < 600) {
            return true;
        }

        // 429 限流错误应该重试
        if (error.status === 429) {
            return true;
        }

        // Gemini 2.5 特殊错误处理
        if (isGemini25) {
            // 某些 Gemini 2.5 特有的临时错误
            if (error.message?.includes('model is overloaded') ||
                error.message?.includes('temporarily unavailable') ||
                error.message?.includes('service unavailable')) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取用户友好的错误消息
     */
    private getErrorMessage(error: any, isGemini25: boolean): string {
        if (error.message?.includes('API key')) {
            return 'API密钥配置错误，请联系管理员';
        }

        if (error.message?.includes('quota') || error.message?.includes('limit')) {
            return 'API配额不足或请求频率过高，请稍后重试';
        }

        if (error.message?.includes('timeout')) {
            return isGemini25 ? 'Gemini 2.5 响应超时，请稍后重试' : '请求超时，请稍后重试';
        }

        if (error.message?.includes('ECONNRESET') || error.message?.includes('network')) {
            return '网络连接不稳定，请检查网络或稍后重试';
        }

        if (isGemini25 && error.message?.includes('model')) {
            return 'Gemini 2.5 模型暂时不可用，请稍后重试或切换到其他模型';
        }

        return error.message || 'Google AI 服务错误';
    }

    async testConnection(): Promise<boolean> {
        try {
            logger.debug('测试 Google AI 连接');
            
            // 发送简单的测试消息
            const result = await this.model.generateContent('Hello');
            const response = await result.response;
            const text = response.text();
            
            logger.debug('Google AI 连接测试成功');
            return !!text;
        } catch (error: any) {
            logger.error('Google AI 连接测试失败:', error);
            return false;
        }
    }

    async getModels(): Promise<string[]> {
        try {
            // Google AI SDK 目前不提供模型列表API
            // 返回常用的模型列表
            return [
                'gemini-1.5-pro',
                'gemini-1.5-flash',
                'gemini-1.0-pro',
                'gemini-pro-vision'
            ];
        } catch (error: any) {
            logger.error('获取 Google AI 模型列表失败:', error);
            return [];
        }
    }

    dispose(): void {
        // Google AI SDK 不需要显式释放资源
        logger.debug('Google AI 客户端已释放');
    }
}

/**
 * Google AI客户端工厂
 */
export class GoogleAIClientFactory implements IAIClientFactory {
    public readonly provider = 'google';

    async createClient(config: IAIServiceConfig, modelName: string): Promise<IAIClient> {
        try {
            logger.info(`创建 Google AI 客户端: ${config.name} - ${modelName}`);
            
            // 解密API密钥
            const apiKey = decryptApiKey(config.apiKey);
            
            // 创建Google AI实例
            const genAI = new GoogleGenerativeAI(apiKey);
            
            // 创建客户端
            const client = new GoogleAIClient(
                config._id.toString(),
                modelName,
                genAI
            );
            
            logger.info(`Google AI 客户端创建成功: ${config.name}`);
            return client;
            
        } catch (error: any) {
            logger.error('创建 Google AI 客户端失败:', error);
            throw new AIClientError(
                `创建 Google AI 客户端失败: ${error.message}`,
                this.provider
            );
        }
    }

    async validateConfig(config: IAIServiceConfig): Promise<boolean> {
        try {
            logger.debug(`验证 Google AI 配置: ${config.name}`);
            
            // 检查必需字段
            if (!config.apiKey) {
                logger.error('Google AI 配置缺少 API 密钥');
                return false;
            }

            // 解密并验证API密钥格式
            const apiKey = decryptApiKey(config.apiKey);
            if (!apiKey.startsWith('AIza')) {
                logger.error('Google AI API 密钥格式无效');
                return false;
            }

            // 创建临时客户端测试连接
            const genAI = new GoogleGenerativeAI(apiKey);
            const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
            
            const result = await model.generateContent('test');
            await result.response;
            
            logger.debug('Google AI 配置验证成功');
            return true;
            
        } catch (error: any) {
            logger.error('Google AI 配置验证失败:', error);
            return false;
        }
    }

    getDefaultConfig(): Partial<IAIServiceConfig> {
        return {
            provider: 'google',
            baseURL: 'https://generativelanguage.googleapis.com',
            maxRetries: 3,
            timeout: 60000,
            rateLimits: {
                requestsPerMinute: 60,
                tokensPerMinute: 100000
            }
        };
    }
}

export default GoogleAIClientFactory;
