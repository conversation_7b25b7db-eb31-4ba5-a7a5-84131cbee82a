import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import AIConfigManager from '../../services/AIConfigManager';
// Confirmed via mcp-feedback-enhanced: 将流程中的动态引入统一提升至头部静态引入
import activeAIConfigManager from '../../services/ActiveAIConfigManager';
import AIServiceModel from '../../models/AIServiceModel';
import { maskApiKey } from '../../utils/encryption';
import { logger } from '../../business/logger';

// 确保可以访问admin类型定义
declare global {
    namespace Express {
        interface Request {
            admin?: {
                id: string;
                username: string;
                email?: string;
                role: string;
                permissions: string[];
            };
        }
    }
}

export class AIConfigController {
    /**
     * 获取AI配置列表
     */
    public async getConfigs(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { page = 1, limit = 10, search, provider, isActive } = req.query;
            
            const result = await AIConfigManager.getConfigs({
                page: parseInt(page as string),
                limit: parseInt(limit as string),
                search: search as string,
                provider: provider as string,
                isActive: isActive === 'true' ? true : isActive === 'false' ? false : undefined
            });
            
            // 隐藏敏感信息
            const safeConfigs = result.configs.map(config => {
                const configObj = config.toObject();
                if (configObj.apiKey) {
                    configObj.apiKey = maskApiKey(configObj.apiKey);
                }
                return configObj;
            });
            
            res.json({
                success: true,
                data: {
                    ...result,
                    configs: safeConfigs
                }
            });
        } catch (error) {
            logger.error('获取AI配置列表失败:', error);
            next(error);
        }
    }

    /**
     * 获取单个AI配置
     */
    public async getConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const config = await AIConfigManager.getConfigById(id);
            
            // 隐藏敏感信息
            const configObj = config.toObject();
            if (configObj.apiKey) {
                configObj.apiKey = maskApiKey(configObj.apiKey);
            }
            
            res.json({
                success: true,
                data: configObj
            });
        } catch (error) {
            logger.error('获取AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 创建AI配置
     */
    public async createConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: '参数验证失败',
                    errors: errors.array()
                });
                return;
            }

            // 从admin认证中间件获取用户ID
            const userId = req.admin?.id;
            if (!userId) {
                res.status(401).json({
                    success: false,
                    message: '用户未认证'
                });
                return;
            }

            const config = await AIConfigManager.createConfig(req.body, userId);
            
            // 隐藏敏感信息
            const configObj = config.toObject();
            if (configObj.apiKey) {
                configObj.apiKey = maskApiKey(configObj.apiKey);
            }
            
            res.status(201).json({
                success: true,
                message: 'AI配置创建成功',
                data: configObj
            });
        } catch (error) {
            logger.error('创建AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 更新AI配置
     */
    public async updateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: '参数验证失败',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const config = await AIConfigManager.updateConfig(id, req.body);
            
            // 隐藏敏感信息
            const configObj = config.toObject();
            if (configObj.apiKey) {
                configObj.apiKey = maskApiKey(configObj.apiKey);
            }
            
            res.json({
                success: true,
                message: 'AI配置更新成功',
                data: configObj
            });
        } catch (error) {
            logger.error('更新AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 删除AI配置
     */
    public async deleteConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            await AIConfigManager.deleteConfig(id);
            
            res.json({
                success: true,
                message: 'AI配置删除成功'
            });
        } catch (error) {
            logger.error('删除AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 测试AI配置连接
     */
    public async testConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const result = await AIConfigManager.testConfig(id);
            
            res.json({
                success: true,
                data: result
            });
        } catch (error) {
            logger.error('测试AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 设置默认配置
     */
    public async setDefaultConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            await AIConfigManager.setDefaultConfig(id);
            
            res.json({
                success: true,
                message: '默认配置设置成功'
            });
        } catch (error) {
            logger.error('设置默认配置失败:', error);
            next(error);
        }
    }

    /**
     * 同步模型列表
     */
    public async syncModels(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.params;
            // 允许前端在本地模式下直接上传已解析的模型列表，或仅上传本地Token由后端拉取
            const localModels: string[] | undefined = Array.isArray(req.body?.localModels)
                ? (req.body.localModels as string[])
                : undefined;
            const isLocal: boolean = Boolean(req.body?.isLocal);
            const localToken: string | undefined = typeof req.body?.localToken === 'string' && req.body.localToken.trim()
                ? req.body.localToken.trim()
                : undefined;

            const models = await AIConfigManager.syncModels(configId, {
                modelsOverride: localModels,
                isLocal,
                localToken,
            });
            
            res.json({
                success: true,
                message: '模型同步成功',
                data: models
            });
        } catch (error) {
            logger.error('同步模型失败:', error);
            next(error);
        }
    }

    /**
     * 获取模型列表
     */
    public async getModels(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId, page = 1, limit = 20, search } = req.query;
            
            const query: any = {};
            if (configId) query.configId = configId;
            if (search) {
                query.$or = [
                    { modelName: { $regex: search, $options: 'i' } },
                    { displayName: { $regex: search, $options: 'i' } }
                ];
            }
            
            const total = await AIServiceModel.countDocuments(query);
            const models = await AIServiceModel.find(query)
                .sort({ sortOrder: 1, modelName: 1 })
                .skip((parseInt(page as string) - 1) * parseInt(limit as string))
                .limit(parseInt(limit as string))
                .populate('configId', 'name provider');
            
            res.json({
                success: true,
                data: {
                    models,
                    total,
                    page: parseInt(page as string),
                    limit: parseInt(limit as string)
                }
            });
        } catch (error) {
            logger.error('获取模型列表失败:', error);
            next(error);
        }
    }

    /**
     * 更新模型配置
     */
    public async updateModel(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const model = await AIServiceModel.findByIdAndUpdate(
                id,
                req.body,
                { new: true, runValidators: true }
            );
            
            if (!model) {
                res.status(404).json({
                    success: false,
                    message: '模型不存在'
                });
                return;
            }
            
            res.json({
                success: true,
                message: '模型更新成功',
                data: model
            });
        } catch (error) {
            logger.error('更新模型失败:', error);
            next(error);
        }
    }

    /**
     * 获取使用统计
     */
    public async getUsageStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.params;
            const { startDate, endDate } = req.query;
            
            const stats = await AIConfigManager.getUsageStats(
                configId,
                startDate ? new Date(startDate as string) : undefined,
                endDate ? new Date(endDate as string) : undefined
            );
            
            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            logger.error('获取使用统计失败:', error);
            next(error);
        }
    }

    /**
     * 启用AI配置
     */
    public async activateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: '参数验证失败',
                    errors: errors.array()
                });
                return;
            }

            const { id: configId } = req.params;
            const { modelId } = req.body;
            const userId = req.admin?.id;

            logger.info(`激活配置请求: configId=${configId}, modelId=${modelId}, userId=${userId}, admin=${JSON.stringify(req.admin)}`);

            if (!userId) {
                logger.error('用户未认证，req.admin:', req.admin);
                res.status(401).json({
                    success: false,
                    message: '用户未认证'
                });
                return;
            }

            if (!modelId) {
                logger.error('模型ID为空');
                res.status(400).json({
                    success: false,
                    message: '模型ID不能为空'
                });
                return;
            }

            const activeConfig = await activeAIConfigManager.activateConfig(configId, modelId, userId);

            res.json({
                success: true,
                message: 'AI配置启用成功',
                data: activeConfig
            });
        } catch (error) {
            logger.error('启用AI配置失败:', error);
            next(error);
        }
    }

    /**
     * 获取当前启用的配置
     */
    public async getActiveConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const activeConfig = await activeAIConfigManager.getActiveConfig();

            res.json({
                success: true,
                data: activeConfig
            });
        } catch (error) {
            logger.error('获取启用配置失败:', error);
            next(error);
        }
    }

    /**
     * 禁用当前配置
     */
    public async deactivateConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            await activeAIConfigManager.deactivateConfig();

            res.json({
                success: true,
                message: '配置已禁用'
            });
        } catch (error) {
            logger.error('禁用配置失败:', error);
            next(error);
        }
    }

    /**
     * 获取配置的可用模型
     */
    public async getConfigModels(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: '参数验证失败',
                    errors: errors.array()
                });
                return;
            }

            const { id: configId } = req.params;
            const models = await activeAIConfigManager.getAvailableModels(configId);

            res.json({
                success: true,
                data: models
            });
        } catch (error) {
            logger.error('获取配置模型失败:', error);
            next(error);
        }
    }

    /**
     * 获取启用配置统计
     */
    public async getActiveConfigStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const stats = await activeAIConfigManager.getActiveConfigStats();

            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            logger.error('获取启用配置统计失败:', error);
            next(error);
        }
    }

    /**
     * 手动添加模型
     */
    public async addManualModels(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.params;
            const { models } = req.body;

            if (!Array.isArray(models) || models.length === 0) {
                res.status(400).json({
                    success: false,
                    message: '请提供有效的模型列表'
                });
                return;
            }

            const addedModels = await AIConfigManager.addManualModels(configId, models);

            res.json({
                success: true,
                message: `成功添加 ${addedModels.length} 个模型`,
                data: addedModels
            });
        } catch (error) {
            logger.error('手动添加模型失败:', error);
            next(error);
        }
    }

    /**
     * 删除手动添加的模型
     */
    public async deleteManualModel(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId, modelId } = req.params;

            const result = await AIConfigManager.deleteManualModel(configId, modelId);

            res.json({
                success: true,
                message: '模型删除成功',
                data: result
            });
        } catch (error) {
            logger.error('删除手动模型失败:', error);
            next(error);
        }
    }
}

export default new AIConfigController();
