/**
 * 定价信息管理控制器
 * 提供价格策略的管理、价格历史记录等功能
 */

import { Request, Response, NextFunction } from 'express';
import Pricing from '../../models/Pricing';
import BalanceTransaction from '../../models/BalanceTransaction';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';
// Confirmed via mcp-feedback-enhanced: 将流程中的动态引入统一提升至头部静态引入
import OpenAIClientFactory from '../../services/OpenAIClientFactory';
import AIServiceConfig from '../../models/AIServiceConfig';

export class PricingController {
    /**
     * 获取定价列表
     */
    public async getPricings(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                search,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (search) {
                query.$or = [
                    { modelName: { $regex: search, $options: 'i' } },
                    { 'alias.zh_CN': { $regex: search, $options: 'i' } },
                    { 'alias.en': { $regex: search, $options: 'i' } }
                ];
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询定价信息
            const [pricings, total] = await Promise.all([
                Pricing.find(query)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                Pricing.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    pricings,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get pricings failed:', error);
            next(error);
        }
    }

    /**
     * 获取定价详情
     */
    public async getPricingById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const pricing = await Pricing.findById(id).lean();

            if (!pricing) {
                res.status(404).json({
                    success: false,
                    message: 'Pricing not found'
                });
                return;
            }

            // 获取使用统计
            const usageStats = await this.getPricingUsageStats(id);

            res.json({
                success: true,
                data: {
                    ...pricing,
                    usageStats
                }
            });

        } catch (error) {
            logger.error('Get pricing by ID failed:', error);
            next(error);
        }
    }

    /**
     * 创建定价
     */
    public async createPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { modelName, inPrice, outPrice, count, alias, intro } = req.body;

            // 检查模型名称是否已存在
            const existingPricing = await Pricing.findOne({ modelName });
            if (existingPricing) {
                res.status(409).json({
                    success: false,
                    message: 'Model name already exists'
                });
                return;
            }

            // 设置默认价格：如果价格为0或未设置，使用默认值（输入输出均为5每3000字符）
            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
            const finalInPrice = (inPrice === 0 || inPrice === undefined) ? DEFAULT_PRICE : inPrice;
            const finalOutPrice = (outPrice === 0 || outPrice === undefined) ? DEFAULT_PRICE : outPrice;
            const finalCount = count || 3000; // 默认3000字符

            const pricing = new Pricing({
                modelName,
                inPrice: finalInPrice,
                outPrice: finalOutPrice,
                count: finalCount,
                alias: new Map(Object.entries(alias || {})),
                intro: new Map(Object.entries(intro || {}))
            });

            await pricing.save();

            logger.info(`Pricing created: ${modelName}`);

            res.status(201).json({
                success: true,
                data: pricing,
                message: 'Pricing created successfully'
            });

        } catch (error) {
            logger.error('Create pricing failed:', error);
            next(error);
        }
    }

    /**
     * 更新定价
     */
    public async updatePricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const updateData = req.body;

            // 如果更新模型名称，检查是否已存在
            if (updateData.modelName) {
                const existingPricing = await Pricing.findOne({
                    modelName: updateData.modelName,
                    _id: { $ne: id }
                });

                if (existingPricing) {
                    res.status(409).json({
                        success: false,
                        message: 'Model name already exists'
                    });
                    return;
                }
            }

            // 设置默认价格：如果价格为0，使用默认值（输入输出均为5每3000字符）
            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
            if (updateData.inPrice === 0) {
                updateData.inPrice = DEFAULT_PRICE;
            }
            if (updateData.outPrice === 0) {
                updateData.outPrice = DEFAULT_PRICE;
            }

            // 处理Map类型字段
            if (updateData.alias) {
                updateData.alias = new Map(Object.entries(updateData.alias));
            }
            if (updateData.intro) {
                updateData.intro = new Map(Object.entries(updateData.intro));
            }

            const pricing = await Pricing.findByIdAndUpdate(
                id,
                updateData,
                { new: true, runValidators: true }
            );

            if (!pricing) {
                res.status(404).json({
                    success: false,
                    message: 'Pricing not found'
                });
                return;
            }

            logger.info(`Pricing updated: ${pricing.modelName}`);

            res.json({
                success: true,
                data: pricing,
                message: 'Pricing updated successfully'
            });

        } catch (error) {
            logger.error('Update pricing failed:', error);
            next(error);
        }
    }

    /**
     * 删除定价
     */
    public async deletePricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            // 检查是否有关联的交易记录
            const transactionCount = await BalanceTransaction.countDocuments({ modelId: id });
            if (transactionCount > 0) {
                res.status(400).json({
                    success: false,
                    message: `Cannot delete pricing. ${transactionCount} transactions are associated with this pricing model.`
                });
                return;
            }

            const pricing = await Pricing.findByIdAndDelete(id);

            if (!pricing) {
                res.status(404).json({
                    success: false,
                    message: 'Pricing not found'
                });
                return;
            }

            logger.info(`Pricing deleted: ${pricing.modelName}`);

            res.json({
                success: true,
                message: 'Pricing deleted successfully'
            });

        } catch (error) {
            logger.error('Delete pricing failed:', error);
            next(error);
        }
    }

    /**
     * 批量更新定价
     */
    public async batchUpdatePricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { pricingIds, updates } = req.body;

            if (!Array.isArray(pricingIds) || pricingIds.length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'Pricing IDs array is required'
                });
                return;
            }

            if (!updates || typeof updates !== 'object') {
                res.status(400).json({
                    success: false,
                    message: 'Updates object is required'
                });
                return;
            }

            // 过滤允许批量更新的字段
            const allowedFields = ['inPrice', 'outPrice', 'count'];
            const filteredUpdates: any = {};
            
            for (const field of allowedFields) {
                if (updates.hasOwnProperty(field)) {
                    filteredUpdates[field] = updates[field];
                }
            }

            if (Object.keys(filteredUpdates).length === 0) {
                res.status(400).json({
                    success: false,
                    message: 'No valid fields to update'
                });
                return;
            }

            const result = await Pricing.updateMany(
                { _id: { $in: pricingIds } },
                filteredUpdates
            );

            logger.info(`Batch updated ${result.modifiedCount} pricings with fields: ${Object.keys(filteredUpdates).join(', ')}`);

            res.json({
                success: true,
                data: {
                    affected: result.modifiedCount,
                    total: pricingIds.length,
                    updates: filteredUpdates
                },
                message: `Successfully updated ${result.modifiedCount} pricings`
            });

        } catch (error) {
            logger.error('Batch update pricing failed:', error);
            next(error);
        }
    }

    /**
     * 获取定价统计信息
     */
    public async getPricingStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const [
                totalPricings,
                priceRanges,
                modelUsage,
                revenueStats
            ] = await Promise.all([
                // 总定价数量
                Pricing.countDocuments({}),

                // 价格区间分布
                Pricing.aggregate([
                    {
                        $project: {
                            modelName: 1,
                            avgPrice: { $avg: ['$inPrice', '$outPrice'] }
                        }
                    },
                    {
                        $bucket: {
                            groupBy: '$avgPrice',
                            boundaries: [0, 0.01, 0.1, 1, 10, 100, Infinity],
                            default: 'Other',
                            output: {
                                count: { $sum: 1 },
                                models: { $push: '$modelName' }
                            }
                        }
                    }
                ]),

                // 模型使用统计
                BalanceTransaction.aggregate([
                    {
                        $match: {
                            modelId: { $exists: true, $ne: null }
                        }
                    },
                    {
                        $lookup: {
                            from: 'pricings',
                            localField: 'modelId',
                            foreignField: '_id',
                            as: 'pricing'
                        }
                    },
                    { $unwind: '$pricing' },
                    {
                        $group: {
                            _id: '$modelId',
                            modelName: { $first: '$pricing.modelName' },
                            usageCount: { $sum: 1 },
                            totalRevenue: { $sum: '$amount' },
                            avgTransactionAmount: { $avg: '$amount' }
                        }
                    },
                    { $sort: { usageCount: -1 } },
                    { $limit: 10 }
                ]),

                // 收入统计
                BalanceTransaction.aggregate([
                    {
                        $match: {
                            modelId: { $exists: true, $ne: null },
                            type: 3 // 消费类型
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            totalRevenue: { $sum: '$amount' },
                            totalTransactions: { $sum: 1 },
                            avgRevenue: { $avg: '$amount' }
                        }
                    }
                ])
            ]);

            const stats = {
                overview: {
                    totalPricings,
                    totalRevenue: revenueStats[0]?.totalRevenue || 0,
                    totalTransactions: revenueStats[0]?.totalTransactions || 0,
                    avgRevenue: revenueStats[0]?.avgRevenue || 0
                },
                priceRanges: priceRanges.map(range => ({
                    ...range,
                    range: this.getPriceRangeLabel(range._id)
                })),
                topModels: modelUsage
            };

            res.json({
                success: true,
                data: stats
            });

        } catch (error) {
            logger.error('Get pricing stats failed:', error);
            next(error);
        }
    }

    /**
     * 导出定价数据
     */
    public async exportPricings(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { format = 'csv', includeUsage = 'false' } = req.query;

            // 查询定价数据
            const pricings = await Pricing.find({})
                .sort({ createdAt: -1 })
                .lean();

            // 格式化数据
            let formattedData = pricings.map((pricing: any) => ({
                id: pricing._id.toString(),
                modelName: pricing.modelName,
                inPrice: pricing.inPrice,
                outPrice: pricing.outPrice,
                count: pricing.count,
                aliasZhCN: pricing.alias?.get('zh_CN') || '',
                aliasEn: pricing.alias?.get('en') || '',
                introZhCN: pricing.intro?.get('zh_CN') || '',
                introEn: pricing.intro?.get('en') || ''
            }));

            // 如果需要包含使用统计
            if (includeUsage === 'true') {
                const usageStats = await this.getAllPricingUsageStats();
                const usageMap = new Map(usageStats.map(stat => [stat._id.toString(), stat]));

                formattedData = formattedData.map(pricing => ({
                    ...pricing,
                    usageCount: usageMap.get(pricing.id)?.usageCount || 0,
                    totalRevenue: usageMap.get(pricing.id)?.totalRevenue || 0
                }));
            }

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `pricings_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} pricings in ${format} format`);

        } catch (error) {
            logger.error('Export pricings failed:', error);
            next(error);
        }
    }

    /**
     * 复制定价模型
     */
    public async duplicatePricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;
            const { newModelName } = req.body;

            if (!newModelName) {
                res.status(400).json({
                    success: false,
                    message: 'New model name is required'
                });
                return;
            }

            // 检查原定价是否存在
            const originalPricing = await Pricing.findById(id);
            if (!originalPricing) {
                res.status(404).json({
                    success: false,
                    message: 'Original pricing not found'
                });
                return;
            }

            // 检查新模型名称是否已存在
            const existingPricing = await Pricing.findOne({ modelName: newModelName });
            if (existingPricing) {
                res.status(409).json({
                    success: false,
                    message: 'New model name already exists'
                });
                return;
            }

            // 创建新定价
            const newPricing = new Pricing({
                modelName: newModelName,
                inPrice: originalPricing.inPrice,
                outPrice: originalPricing.outPrice,
                count: originalPricing.count,
                alias: originalPricing.alias,
                intro: originalPricing.intro
            });

            await newPricing.save();

            logger.info(`Pricing duplicated: ${originalPricing.modelName} -> ${newModelName}`);

            res.status(201).json({
                success: true,
                data: newPricing,
                message: 'Pricing duplicated successfully'
            });

        } catch (error) {
            logger.error('Duplicate pricing failed:', error);
            next(error);
        }
    }

    /**
     * 检查并修复价格为0的模型
     */
    public async fixZeroPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符

            // 查找价格为0的模型
            const zeroPricingModels = await Pricing.find({
                $or: [
                    { inPrice: 0 },
                    { outPrice: 0 }
                ]
            });

            if (zeroPricingModels.length === 0) {
                res.json({
                    success: true,
                    message: 'No models with zero pricing found',
                    data: { fixed: 0 }
                });
                return;
            }

            // 批量更新价格为0的模型
            const bulkOps = zeroPricingModels.map(model => ({
                updateOne: {
                    filter: { _id: model._id },
                    update: {
                        $set: {
                            inPrice: model.inPrice === 0 ? DEFAULT_PRICE : model.inPrice,
                            outPrice: model.outPrice === 0 ? DEFAULT_PRICE : model.outPrice
                        }
                    }
                }
            }));

            const result = await Pricing.bulkWrite(bulkOps);

            logger.info(`Fixed ${result.modifiedCount} models with zero pricing`);

            res.json({
                success: true,
                message: `Successfully fixed ${result.modifiedCount} models with zero pricing`,
                data: {
                    fixed: result.modifiedCount,
                    defaultPrice: DEFAULT_PRICE,
                    models: zeroPricingModels.map(m => m.modelName)
                }
            });

        } catch (error) {
            logger.error('Fix zero pricing failed:', error);
            next(error);
        }
    }

    /**
     * 为当前使用的模型创建价格配置
     */
    public async createCurrentModelPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 获取当前使用的模型信息（头部静态引入）
            const aiConfigInfo = await OpenAIClientFactory.getDefaultClientWithModel();

            if (!aiConfigInfo.modelName) {
                res.status(400).json({
                    success: false,
                    message: 'Unable to get current model information'
                });
                return;
            }

            // 检查是否已存在该模型的价格配置
            const existingPricing = await Pricing.findOne({ modelName: aiConfigInfo.modelName });
            if (existingPricing) {
                res.json({
                    success: true,
                    message: 'Pricing already exists for current model',
                    data: existingPricing
                });
                return;
            }

            // 创建默认价格配置
            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
            const pricing = new Pricing({
                modelName: aiConfigInfo.modelName,
                inPrice: DEFAULT_PRICE,
                outPrice: DEFAULT_PRICE,
                count: 3000,
                alias: new Map([
                    ['zh', aiConfigInfo.modelName],
                    ['en', aiConfigInfo.modelName]
                ]),
                intro: new Map([
                    ['zh', `当前使用的模型 ${aiConfigInfo.modelName}，配置来源：${aiConfigInfo.configName}`],
                    ['en', `Current model ${aiConfigInfo.modelName}, from config: ${aiConfigInfo.configName}`]
                ])
            });

            await pricing.save();

            logger.info(`为当前模型创建价格配置: ${aiConfigInfo.modelName} (来源: ${aiConfigInfo.configName})`);

            res.json({
                success: true,
                message: 'Successfully created pricing for current model',
                data: {
                    pricing,
                    modelName: aiConfigInfo.modelName,
                    configName: aiConfigInfo.configName,
                    isActiveConfig: aiConfigInfo.isActiveConfig,
                    defaultPrice: DEFAULT_PRICE
                }
            });

        } catch (error) {
            logger.error('Create current model pricing failed:', error);
            next(error);
        }
    }

    /**
     * 同步模型并创建价格配置
     */
    public async syncModelsWithPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.body;

            if (!configId) {
                res.status(400).json({
                    success: false,
                    message: 'Config ID is required'
                });
                return;
            }

            // 获取AI配置（头部静态引入）
            const config = await AIServiceConfig.findById(configId).select('+apiKey');

            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'AI service config not found'
                });
                return;
            }

            // 测试连接并获取模型列表（头部静态引入）
            const testResult = await OpenAIClientFactory.testConnection(config);

            if (!testResult.success || !testResult.models) {
                res.status(400).json({
                    success: false,
                    message: `Unable to get model list: ${testResult.error}`
                });
                return;
            }

            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
            const syncResults = {
                created: 0,
                updated: 0,
                skipped: 0,
                errors: 0,
                models: [] as any[]
            };

            // 为每个模型创建或更新价格配置
            for (const modelName of testResult.models) {
                try {
                    // 检查是否已存在
                    const existingPricing = await Pricing.findOne({ modelName });

                    if (existingPricing) {
                        // 更新现有配置的同步信息
                        existingPricing.isSynced = true;
                        existingPricing.syncedAt = new Date();
                        existingPricing.sourceConfigId = configId;

                        // 更新模型信息
                        existingPricing.modelInfo = new Map([
                            ['configName', config.name],
                            ['provider', config.provider],
                            ['baseURL', config.baseURL],
                            ['syncedAt', new Date().toISOString()]
                        ]);

                        // 更新完整模型数据
                        existingPricing.modelData = {
                            modelName,
                            configId,
                            configName: config.name,
                            provider: config.provider,
                            baseURL: config.baseURL,
                            syncedAt: new Date(),
                            pricing: {
                                inPrice: existingPricing.inPrice,
                                outPrice: existingPricing.outPrice,
                                count: existingPricing.count
                            }
                        };

                        await existingPricing.save();
                        syncResults.updated++;
                        syncResults.models.push({
                            modelName,
                            action: 'updated',
                            pricing: existingPricing
                        });
                    } else {
                        // 创建新的价格配置
                        const pricing = new Pricing({
                            modelName,
                            inPrice: DEFAULT_PRICE,
                            outPrice: DEFAULT_PRICE,
                            count: 3000,
                            alias: new Map([
                                ['zh', modelName],
                                ['en', modelName]
                            ]),
                            intro: new Map([
                                ['zh', `通过同步创建的模型 ${modelName}，来源配置：${config.name}`],
                                ['en', `Model ${modelName} created by sync, from config: ${config.name}`]
                            ]),
                            modelInfo: new Map([
                                ['configName', config.name],
                                ['provider', config.provider],
                                ['baseURL', config.baseURL],
                                ['syncedAt', new Date().toISOString()]
                            ]),
                            modelData: {
                                modelName,
                                configId,
                                configName: config.name,
                                provider: config.provider,
                                baseURL: config.baseURL,
                                syncedAt: new Date(),
                                pricing: {
                                    inPrice: DEFAULT_PRICE,
                                    outPrice: DEFAULT_PRICE,
                                    count: 3000
                                }
                            },
                            isSynced: true,
                            syncedAt: new Date(),
                            sourceConfigId: configId
                        });

                        await pricing.save();
                        syncResults.created++;
                        syncResults.models.push({
                            modelName,
                            action: 'created',
                            pricing
                        });
                    }
                } catch (error) {
                    logger.error(`同步模型 ${modelName} 失败:`, error);
                    syncResults.errors++;
                    syncResults.models.push({
                        modelName,
                        action: 'error',
                        error: error.message
                    });
                }
            }

            logger.info(`模型同步完成: 创建 ${syncResults.created}, 更新 ${syncResults.updated}, 跳过 ${syncResults.skipped}, 错误 ${syncResults.errors}`);

            res.json({
                success: true,
                message: `Successfully synced ${testResult.models.length} models`,
                data: {
                    configName: config.name,
                    totalModels: testResult.models.length,
                    defaultPrice: DEFAULT_PRICE,
                    results: syncResults
                }
            });

        } catch (error) {
            logger.error('Sync models with pricing failed:', error);
            next(error);
        }
    }

    /**
     * 获取定价使用统计
     */
    private async getPricingUsageStats(pricingId: string) {
        const stats = await BalanceTransaction.aggregate([
            { $match: { modelId: pricingId } },
            {
                $group: {
                    _id: null,
                    usageCount: { $sum: 1 },
                    totalRevenue: { $sum: '$amount' },
                    avgTransactionAmount: { $avg: '$amount' },
                    lastUsed: { $max: '$timestamp' }
                }
            }
        ]);

        return stats[0] || {
            usageCount: 0,
            totalRevenue: 0,
            avgTransactionAmount: 0,
            lastUsed: null
        };
    }

    /**
     * 获取所有定价的使用统计
     */
    private async getAllPricingUsageStats() {
        return await BalanceTransaction.aggregate([
            {
                $match: {
                    modelId: { $exists: true, $ne: null }
                }
            },
            {
                $group: {
                    _id: '$modelId',
                    usageCount: { $sum: 1 },
                    totalRevenue: { $sum: '$amount' },
                    avgTransactionAmount: { $avg: '$amount' },
                    lastUsed: { $max: '$timestamp' }
                }
            }
        ]);
    }

    /**
     * 获取价格区间标签
     */
    private getPriceRangeLabel(boundary: any): string {
        if (boundary === 'Other') return '其他';
        if (boundary === 0) return '0-0.01';
        if (boundary === 0.01) return '0.01-0.1';
        if (boundary === 0.1) return '0.1-1';
        if (boundary === 1) return '1-10';
        if (boundary === 10) return '10-100';
        if (boundary === 100) return '100+';
        return '未知';
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = Object.keys(data[0]);

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new PricingController();
