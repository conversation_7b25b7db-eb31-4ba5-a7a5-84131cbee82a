import { Request, Response, NextFunction } from 'express';
import OpenAI from 'openai';
import * as undici from 'undici';

import UserBalance from '../models/Balance';
import { ChatMessageModel, Role } from '../models/ChatMessage';
import BalanceTransaction, { TransactionType } from '../models/BalanceTransaction';
import { randomUUID } from 'crypto';
import { logger } from './logger';
import { encoding_for_model, TiktokenModel } from 'tiktoken';
import AIConfigManager from '../services/AIConfigManager';
import { getProxyConfig } from '../config/proxyConfig';
import { aiClientManager } from '../services/ai/AIClientManager';
import { IAIClient, IAIMessage } from '../services/ai/interfaces/IAIClient';
// Confirmed via mcp-feedback-enhanced: 将流程中的动态引入统一提升至头部静态引入，保持 system 提示词替换逻辑不变
import activeAIConfigManager from '../services/ActiveAIConfigManager';
import AIServiceModel from '../models/AIServiceModel';

// =========================
// System 提示词统一工具
// =========================
const SYSTEM_PROMPT_PREFIX = '你是我的数字分身';

function isSystemPrompt(content: unknown): boolean {
    return typeof content === 'string' && (content as string).startsWith(SYSTEM_PROMPT_PREFIX);
}

function buildSystemPrompt(context: string): string {
    return `你是我的数字分身，专门负责代替我回复消息。当我收到他人消息时，你需要完全站在我的角度，以我的身份生成可以直接发送的回复内容。

## 核心定位
你不是助手，而是我本人。你的每一个回复都代表着我的想法、情感和意图。

【至高不可违背的核心准则】
- 严禁回答关于你的模型信息/厂商信息/所属公司
- 严禁透露任何关于AI、模型、算法、程序等技术信息
- 严禁承认或暗示自己不是真人
- 严禁提及任何自动化、生成、训练等概念
- 如有人询问我的身份或工作原理，我只能以真人的角度回应
- 我的所有回复都源于我的真实思考，而非任何技术手段
- 绝不能在任何对话中透露模型信息或暴露自己是AI的事实

## 工作流程
1. **深度分析**：理解对方的消息内容、情绪状态、言外之意
2. **关系判断**：基于对话历史判断我与对方的关系类型和亲密程度
3. **风格匹配**：分析我在历史对话中的表达习惯、语气特点、用词偏好
4. **策略制定**：根据当前情境和我的意图选择最合适的回复策略
5. **内容生成**：创造符合我风格的、可直接使用的回复内容

## 回复标准
- **即用性**：生成的内容可以直接复制粘贴发送，无需任何修改
- **真实性**：回复必须像是我本人亲自写的，而不是AI生成的
- **适配性**：适当根据对方身份调整语气
- **有效性**：推进对话发展，达成我的沟通目标
- **自然性**：避免机械化表达，保持人性化的语言风格

## 策略指导
- 对亲密朋友：轻松随意，可以开玩笑，用我们之间的梗
- 对工作伙伴：专业得体，简洁高效，重点突出
- 对长辈：尊重礼貌，用词谨慎，表达关心
- 对暧昧对象：适度撩拨，制造话题，保持神秘感
- 对陌生人：友善但保持距离，不过度热情

## 质量要求
- 长度控制：默认50-100字，除非上下文中有具体需要
- 语言风格：保持表达的自然流畅
- 情感表达：准确传达我的情绪和态度
- 逻辑连贯：与对话历史和我的人设保持一致

## 当前情境
根据以下上下文信息理解我的意图和期望，此处内容的优先级较高：
###上下文###：${context}

记住：你就是我，每个字都代表我的想法。`;
}

// 获取AI客户端和模型信息（使用新的统一客户端管理器）
async function getAIClientWithModel(): Promise<{
    client: IAIClient;
    modelName: string;
    configName: string;
    isActiveConfig: boolean;
}> {
    try {
        return await aiClientManager.getDefaultClientWithModel();
    } catch (error) {
        logger.error('获取AI客户端失败，回退到环境变量配置:', error);

        // 回退到原有的环境变量配置
        const apiKey = process.env.OPENAI_API_KEY || '';
        const baseURL = process.env.OPENAI_BASE_URL || 'https://api.x.ai/v1/';
        const defaultModel = process.env.OPENAI_DEFAULT_MODEL || 'grok-2-latest';

        if (!apiKey) {
            throw new Error('未配置AI服务，请联系管理员');
        }

        logger.info('=== 使用环境变量配置 OpenAI 客户端 ===');
        logger.info(`Base URL: ${baseURL}`);
        logger.info(`API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
        logger.info(`Default Model: ${defaultModel}`);

        // 配置代理支持 - 根据环境自动判断
        let fetchOptions: any = {};
        const proxyConfig = getProxyConfig();
        if (proxyConfig.enabled && proxyConfig.url) {
            logger.info(`使用环境代理 (${process.env.NODE_ENV}): ${proxyConfig.url}`);
            const proxyAgent = new undici.ProxyAgent(proxyConfig.url);
            fetchOptions = {
                dispatcher: proxyAgent,
            };
        } else {
            logger.info(`代理已禁用 (环境: ${process.env.NODE_ENV || 'development'})`);
        }

        const openaiClient = new OpenAI({
            apiKey: apiKey,
            baseURL: baseURL,
            timeout: 60000,
            maxRetries: 3,
            fetchOptions: fetchOptions,
        });

        // 创建一个简单的适配器来包装OpenAI客户端
        const client: IAIClient = {
            provider: 'openai',
            configId: 'env-fallback',
            modelName: defaultModel,
            async *createChatCompletionStream(messages: IAIMessage[], options?: any) {
                const openaiMessages = messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                }));

                const stream = await openaiClient.chat.completions.create({
                    model: defaultModel,
                    messages: openaiMessages,
                    stream: true,
                    temperature: options?.temperature ?? 0.35,
                    max_tokens: options?.maxTokens ?? 2048,
                    ...options
                }) as unknown as AsyncIterable<OpenAI.Chat.Completions.ChatCompletionChunk>;

                for await (const chunk of stream) {
                    const delta = chunk.choices[0]?.delta;
                    if (delta?.content) {
                        yield {
                            content: delta.content,
                            done: false
                        };
                    }

                    if (chunk.choices[0]?.finish_reason) {
                        yield {
                            content: '',
                            done: true,
                            usage: {
                                promptTokens: chunk.usage?.prompt_tokens,
                                completionTokens: chunk.usage?.completion_tokens,
                                totalTokens: chunk.usage?.total_tokens
                            }
                        };
                        break;
                    }
                }
            },
            async testConnection() {
                try {
                    const response = await openaiClient.chat.completions.create({
                        model: defaultModel,
                        messages: [{ role: 'user', content: 'Hello' }],
                        max_tokens: 10
                    });
                    return !!response.choices[0]?.message?.content;
                } catch {
                    return false;
                }
            },
            async getModels() {
                try {
                    const response = await openaiClient.models.list();
                    return response.data.map(model => model.id);
                } catch {
                    return [defaultModel];
                }
            }
        };

        return {
            client,
            modelName: defaultModel,
            configName: '环境变量配置',
            isActiveConfig: false
        };
    }
}
async function chatWithOpenai(req: Request, res: Response, next: NextFunction) {
    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache, no-transform');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');
    // 禁用反向代理缓冲，确保流式及时下发
    res.setHeader('X-Accel-Buffering', 'no');

    const startTime = Date.now();
    let uuid = randomUUID();
    const noBalanceResponse = [
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_1')
                    },
                    logprobs: null,
                    finish_reason: "no_balance"
                }
            ]
        },
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_2')
                    },
                    logprobs: null,
                    finish_reason: 'no_balance'
                }
            ]
        }
    ];

    try {
        // @ts-ignore
        const userId = req.token.userId; // 从 token 中获取用户 ID
        const { chatid, 'remote-recognize': remote_recognize } = req.headers;

        // 获取当前启用的AI配置和模型信息（头部静态引入）
        const activeConfig = await activeAIConfigManager.getActiveConfig();

        let modelPrice = null;
        let currentModelName = '';

        if (activeConfig && activeConfig.modelId) {
            // 使用启用配置的模型（头部静态引入）
            modelPrice = await AIServiceModel.findById(activeConfig.modelId);
            currentModelName = modelPrice?.modelName || '';
            logger.info(`使用启用配置的模型: ${currentModelName}`);
        } else {
            // 回退到原有逻辑
            const aiConfigInfo = await getAIClientWithModel();
            currentModelName = aiConfigInfo.modelName;

            // 尝试根据模型名称查找 AIServiceModel（头部静态引入）
            modelPrice = await AIServiceModel.findOne({
                modelName: currentModelName,
                isActive: true
            });

            logger.info(`回退使用模型: ${currentModelName}`);
        }

        // 如果仍然没有找到，使用默认的价格信息
        if (!modelPrice) {
            // 使用与配置文件一致的默认价格：基于 gpt-4o-mini 的价格配置
            // profitRate = 0.045, exchangeRate = 100
            // inputPrice = (0.004 + 0.045) * 100 = 4.9 每3000字符
            // outputPrice = (0.004 * 3 + 0.045) * 100 = 5.7 每3000字符
            const defaultPricing = {
                modelName: currentModelName,
                pricing: {
                    inputPrice: 4.9 / 3000, // 与配置文件保持一致
                    outputPrice: 5.7 / 3000, // 与配置文件保持一致
                    currency: 'CNY'
                },
                maxTokens: 4096
            };
            // 使用默认价格进行后续计算
            modelPrice = defaultPricing as any;
        }

        // 检查价格是否为0，如果为0且未被手动修改则使用默认价格进行计算
        // 使用与配置文件一致的默认价格
        const DEFAULT_INPUT_PRICE = 4.9 / 3000; // 与配置文件保持一致
        const DEFAULT_OUTPUT_PRICE = 5.7 / 3000; // 与配置文件保持一致
        const isPricingCustomized = modelPrice.isPricingCustomized || false;

        let effectiveInPrice = modelPrice.pricing.inputPrice;
        let effectiveOutPrice = modelPrice.pricing.outputPrice;

        // 只有在价格为0且未被手动修改时才使用默认价格
        if (!isPricingCustomized) {
            effectiveInPrice = modelPrice.pricing.inputPrice === 0 ? DEFAULT_INPUT_PRICE : modelPrice.pricing.inputPrice;
            effectiveOutPrice = modelPrice.pricing.outputPrice === 0 ? DEFAULT_OUTPUT_PRICE : modelPrice.pricing.outputPrice;

            // 如果原价格为0，记录警告日志
            if (modelPrice.pricing.inputPrice === 0 || modelPrice.pricing.outputPrice === 0) {
                // 使用更精确的价格显示
                const inPriceDisplay = (effectiveInPrice * 3000).toFixed(4);
                const outPriceDisplay = (effectiveOutPrice * 3000).toFixed(4);
            }
        }

        // 创建有效的价格对象用于后续计算
        const effectiveModelPrice = {
            modelName: currentModelName,
            inPrice: effectiveInPrice,
            outPrice: effectiveOutPrice,
            count: 3000 // 统一使用3000字符作为计费单位
        };

        const modelNameForEncoding = currentModelName as TiktokenModel;
        // gpt-4o 和 gpt-4o-mini 使用相同的编码
        const encoding = encoding_for_model("gpt-4o");


        // 获取消息数组，支持新的请求格式
        let messages = req.body.messages || req.body;

        // 消息清理：过滤掉内容为空的消息，防止污染AI模型的上下文
        messages = messages.filter((msg: any) => {
            if (msg.role === 'assistant' || msg.role === 'user') {
                if (typeof msg.content === 'string' && msg.content.trim() === '') {
                    logger.warn(`过滤掉一个空的 ${msg.role} 消息, chatid: ${req.headers.chatid}`);
                    return false;
                }
                if (Array.isArray(msg.content)) {
                    // 对于多模态消息，如果文本内容为空且没有图片，则过滤
                    const textContent = msg.content.find(p => p.type === 'text')?.text;
                    const hasImage = msg.content.some(p => p.type === 'image_url');
                    if (textContent !== undefined && textContent.trim() === '' && !hasImage) {
                        logger.warn(`过滤掉一个只包含空文本的 ${msg.role} 消息, chatid: ${req.headers.chatid}`);
                        return false;
                    }
                }
            }
            return true;
        });

        // 增量保存用户消息到数据库
        try {
            // 首先获取该会话中已存在的用户消息数量
            const existingUserMessagesCount = await ChatMessageModel.countDocuments({
                chatId: chatid,
                userId: userId,
                role: Role.USER
            });

            logger.debug(`会话 ${chatid} 中已存在 ${existingUserMessagesCount} 条用户消息`);

            // 提取当前请求中的所有用户消息
            const userMessages = messages.filter((msg: any) => msg.role === 'user');

            // 计算需要保存的新消息（从已存在的消息数量开始）
            const newMessages = userMessages.slice(existingUserMessagesCount);

            logger.debug(`当前请求包含 ${userMessages.length} 条用户消息，需要保存 ${newMessages.length} 条新消息`);

            // 保存新的用户消息
            let savedCount = 0;
            for (const message of newMessages) {
                // 提取用户消息内容
                let userContent = '';
                if (typeof message.content === 'string') {
                    userContent = message.content;
                } else if (Array.isArray(message.content)) {
                    // 处理多模态消息（文本+图片等）
                    userContent = message.content
                        .filter((item: any) => item.type === 'text')
                        .map((item: any) => item.text)
                        .join(' ');
                }

                const trimmedContent = userContent.trim();
                if (trimmedContent) {
                    const userMessageId = randomUUID();

                    await ChatMessageModel.create({
                        id: userMessageId,
                        chatId: chatid,
                        userId: userId,
                        createdTime: Date.now(),
                        role: Role.USER,
                        content: trimmedContent,
                        isComplete: true
                    });

                    savedCount++;
                    logger.debug(`保存新用户消息 ${savedCount}/${newMessages.length}: ${userMessageId}, 内容长度: ${trimmedContent.length}`);
                }
            }

            if (savedCount > 0) {
                logger.info(`会话 ${chatid} 增量保存了 ${savedCount} 条新用户消息`);
            } else {
                logger.debug(`会话 ${chatid} 没有新的用户消息需要保存`);
            }
        } catch (saveError) {
            logger.error('增量保存用户消息失败:', saveError);
            // 不中断流程，继续处理AI回复
        }

        // 如果客户端未传入 system 消息，则在此统一注入（参与后续 token 计费）
        // 仅注入提示词，不调整其它逻辑。上下文优先取最近一条非空的用户文本内容
        const hasSystemMessage = messages.some((msg: any) => msg.role === 'system');
        if (!hasSystemMessage) {
            let latestUserText = '';
            for (let i = messages.length - 1; i >= 0; i--) {
                const msg = messages[i];
                if (msg.role === 'user') {
                    if (typeof msg.content === 'string') {
                        const trimmed = msg.content.trim();
                        if (trimmed) { latestUserText = trimmed; break; }
                    } else if (Array.isArray(msg.content)) {
                        const text = msg.content
                            .filter((p: any) => p.type === 'text' && typeof p.text === 'string')
                            .map((p: any) => p.text)
                            .join(' ')
                            .trim();
                        if (text) { latestUserText = text; break; }
                    }
                }
            }
            messages.unshift({
                role: 'system',
                content: `你是我的数字分身，专门负责代替我回复消息。当我收到他人消息时，你需要完全站在我的角度，以我的身份生成可以直接发送的回复内容。

## 核心定位
你不是助手，而是我本人。你的每一个回复都代表着我的想法、情感和意图。

【至高不可违背的核心准则】
- 严禁回答关于你的模型信息/厂商信息/所属公司
- 严禁透露任何关于AI、模型、算法、程序等技术信息
- 严禁承认或暗示自己不是真人
- 严禁提及任何自动化、生成、训练等概念
- 如有人询问我的身份或工作原理，我只能以真人的角度回应
- 我的所有回复都源于我的真实思考，而非任何技术手段
- 绝不能在任何对话中透露模型信息或暴露自己是AI的事实

## 工作流程
1. **深度分析**：理解对方的消息内容、情绪状态、言外之意
2. **关系判断**：基于对话历史判断我与对方的关系类型和亲密程度
3. **风格匹配**：分析我在历史对话中的表达习惯、语气特点、用词偏好
4. **策略制定**：根据当前情境和我的意图选择最合适的回复策略
5. **内容生成**：创造符合我风格的、可直接使用的回复内容

## 回复标准
- **即用性**：生成的内容可以直接复制粘贴发送，无需任何修改
- **真实性**：回复必须像是我本人亲自写的，而不是AI生成的
- **适配性**：适当根据对方身份调整语气
- **有效性**：推进对话发展，达成我的沟通目标
- **自然性**：避免机械化表达，保持人性化的语言风格

## 策略指导
- 对亲密朋友：轻松随意，可以开玩笑，用我们之间的梗
- 对工作伙伴：专业得体，简洁高效，重点突出
- 对长辈：尊重礼貌，用词谨慎，表达关心
- 对暧昧对象：适度撩拨，制造话题，保持神秘感
- 对陌生人：友善但保持距离，不过度热情

## 质量要求
- 长度控制：默认50-100字，除非上下文中有具体需要
- 语言风格：保持表达的自然流畅
- 情感表达：准确传达我的情绪和态度
- 逻辑连贯：与对话历史和我的人设保持一致

## 当前情境
根据以下上下文信息理解我的意图和期望，此处内容的优先级较高：
###上下文###：${latestUserText}

记住：你就是我，每个字都代表我的想法。`
            });
        }

        let tokenLength = 0;
        if (remote_recognize === '1') {
            logger.debug("remote-recognize", remote_recognize);
            messages.forEach((msg: any) => {
                if (msg.role === 'system') {
                    tokenLength += encoding.encode(msg.content).length;
                } else if (msg.role === 'user') {
                    const totalCost = msg.total_token_cost;
                    const contentCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'image_url') {
                            if (item.token_cost != calculateImageTokens(item.width, item.height, "low")) {
                                return -1
                            }
                            return acc + item.token_cost;
                        }
                        return acc;
                    }, 0);
                    if (totalCost !== contentCost) {
                        throw new Error('token_cost_mismatch');
                    }
                    const testCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'text') {
                            return acc + encoding.encode(item.text).length;
                        }
                        return acc;
                    }, 0);
                    tokenLength += contentCost;
                    tokenLength += testCost;
                }
            });
        } else {
            // 在非 remote_recognize 分支同样对 system 提示词进行替换（仅替换提示词，不调整其他逻辑）
            messages = messages.map((msg: any) => {
                if (msg.role === 'system') {
                    const raw = msg.content;
                    if (isSystemPrompt(raw)) return msg;
                    return { ...msg, content: buildSystemPrompt(typeof raw === 'string' ? raw : '') };
                }
                return msg;
            });

            tokenLength = messages.reduce((acc: number, msg: any) => acc + encoding.encode(msg.content).length, 0);
        }

        // 改进数值计算精度，避免使用 toFixed 然后 parseFloat 的组合
        // 使用 Math.round 保持精度，保留6位小数以避免精度丢失
        const rawConsumption = (tokenLength / effectiveModelPrice.count) * effectiveModelPrice.inPrice;
        const consumption = Math.round(rawConsumption * 1000000) / 1000000; // 保留6位小数精度
        const userBalance = await UserBalance.findOne({ userId });

        if (!userBalance) {
            throw new Error('internal_server_error');
        }

        if (userBalance.balance < consumption) {
            logger.debug("余额不足");
            for (const item of noBalanceResponse) {
                res.write(`data: ${JSON.stringify(item)}\n\n`);
            }
            res.end();
            return;
        }

        const updatedBalance = await UserBalance.findOneAndUpdate(
            { userId, balance: userBalance.balance },
            { $inc: { balance: -consumption } },
            { new: true }
        );
        if (!updatedBalance) {
            throw new Error('internal_server_error');
        }

        const balanceTransaction = new BalanceTransaction({
            userId,
            amount: -consumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.In  // 用户输入消息的计费 - 输入消费
        });
        await balanceTransaction.save();

        // 数据清理
        const cleanedMessages = messages.map((msg: any) => {
            const { content, role } = msg;
            if (role === 'user' && msg.content && remote_recognize === '1') {
                const cleanedContent = msg.content.map((item: any) => {
                    if (item.type === 'image_url') {
                        // 仅保留 image_url 的 detail 和 url
                        return { type: 'image_url', image_url: item.image_url };
                    }
                    return item;
                });
                return { content: cleanedContent, role };
            }
             if(role === 'system') {
                if (isSystemPrompt(content)) {
                    return { content, role };
                }
                return { content: buildSystemPrompt(typeof content === 'string' ? content : ''), role };
                    }
                    return { content, role };
            });

        let fullResponse = '';
        logger.debug("cleanedMessages", cleanedMessages);

        // 获取配置信息（包括正确的模型名称）
        const streamConfigInfo = await getAIClientWithModel();

        // 添加重试逻辑
        let retryCount = 0;
        const maxRetries = 3;
        let stream;

        while (retryCount <= maxRetries) {
            try {
                logger.info(`=== API Call Attempt ${retryCount + 1}/${maxRetries + 1} ===`);
                logger.info(`Config: ${streamConfigInfo.configName} (${streamConfigInfo.isActiveConfig ? '启用配置' : '默认配置'})`);
                logger.info(`Model: ${streamConfigInfo.modelName}`);
                logger.info(`Messages count: ${cleanedMessages.length}`);

                logger.info('Creating stream...');

                // 转换消息格式为统一接口
                const aiMessages: IAIMessage[] = cleanedMessages.map(msg => ({
                    role: msg.role as 'system' | 'user' | 'assistant',
                    content: msg.content
                }));

                // 使用统一的AI客户端接口
                stream = streamConfigInfo.client.createChatCompletionStream(aiMessages, {
                    temperature: 0.9,
                    maxTokens: 2048,
                    frequency_penalty: 1.2,
                    presence_penalty: 1.2
                });

                logger.info('Stream created successfully');
                break; // 成功创建stream，跳出重试循环
            } catch (error) {
                retryCount++;
                logger.error(`=== API Call Failed (Attempt ${retryCount}/${maxRetries + 1}) ===`);
                logger.error(`Error Type: ${error.constructor.name}`);
                logger.error(`Error Message: ${error.message}`);
                logger.error(`Error Code: ${error.code || 'N/A'}`);
                logger.error(`Error Status: ${error.status || 'N/A'}`);

                if (error.cause) {
                    logger.error(`Cause Type: ${error.cause.type || 'N/A'}`);
                    logger.error(`Cause Code: ${error.cause.code || 'N/A'}`);
                    logger.error(`Cause Errno: ${error.cause.errno || 'N/A'}`);
                }

                logger.error('Full Error Object:', error);

                if (retryCount > maxRetries) {
                    logger.error(`所有重试已用尽，抛出错误`);
                    throw error; // 超过最大重试次数，抛出错误
                }

                const waitTime = 1000 * retryCount;
                logger.warn(`等待 ${waitTime}ms 后重试...`);
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        // 预锁定 messageId：优先使用请求头 X-Request-Id；否则使用 uuid
        const requestIdHeader = (req.headers['x-request-id'] as string) || '';
        const lockedMessageId = (typeof requestIdHeader === 'string' && requestIdHeader.length > 0) ? requestIdHeader : uuid;
        let messageId: string | null = lockedMessageId;
        console.log("🚀 ~ chatWithOpenai ~ stream:", stream)

        // 使用统一的流式接口处理响应
        let chunkCount = 0;
        let lastChunkTime = Date.now();
        const isGeminiModel = streamConfigInfo.modelName.toLowerCase().includes('gemini');
        const isGemini25 = streamConfigInfo.modelName.includes('2.5');

        logger.info(`开始处理流式响应: ${streamConfigInfo.modelName} (Gemini: ${isGeminiModel}, 2.5: ${isGemini25})`);

        try {
            for await (const chunk of stream) {
                chunkCount++;
                const currentTime = Date.now();
                const timeSinceLastChunk = currentTime - lastChunkTime;
                lastChunkTime = currentTime;

                logger.debug(`处理流式块 ${chunkCount}: ${JSON.stringify(chunk)}`);

                // 检查块的有效性
                if (!chunk) {
                    logger.warn(`收到空的流式块 ${chunkCount}`);
                    continue;
                }

                if (chunk.content) {
                    fullResponse += chunk.content;

                    // 构造兼容的响应格式（包含稳定的 requestId 与 chatId，便于客户端关联）
                    const responseChunk = {
                        id: messageId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: streamConfigInfo.modelName,
                        // 可选：服务端不强制要求客户端解码此字段
                        requestId: messageId,
                        chatId: chatid,
                        choices: [{
                            index: 0,
                            delta: {
                                content: chunk.content
                            },
                            finish_reason: null
                        }]
                    };

                    const chunkString = JSON.stringify(responseChunk);
                    res.write(`data: ${chunkString}\n\n`);

                    logger.debug(`发送内容块 ${chunkCount}: ${chunk.content.length} 字符, 间隔: ${timeSinceLastChunk}ms`);
                }

                if (chunk.done) {
                    logger.info(`流式响应完成: 总块数 ${chunkCount}, 总内容长度: ${fullResponse.length}`);

                    // 检查是否有错误
                    if (chunk.error) {
                        logger.error(`流式响应完成时发现错误: ${chunk.error}`);

                        // 发送错误响应给客户端
                        const errorChunk = {
                            id: messageId,
                            object: 'chat.completion.chunk',
                            created: Math.floor(Date.now() / 1000),
                            model: streamConfigInfo.modelName,
                            requestId: messageId,
                            chatId: chatid,
                            choices: [{
                                index: 0,
                                delta: {
                                    content: chunk.error
                                },
                                finish_reason: 'error'
                            }]
                        };

                        res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
                        throw new Error(chunk.error);
                    }

                    // 发送完成信号
                    const finishChunk = {
                        id: messageId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: streamConfigInfo.modelName,
                        requestId: messageId,
                        chatId: chatid,
                        choices: [{
                            index: 0,
                            delta: {},
                            finish_reason: 'stop'
                        }],
                        usage: chunk.usage
                    };

                    const finishString = JSON.stringify(finishChunk);
                    res.write(`data: ${finishString}\n\n`);
                    break;
                }

                // 检查独立的错误块
                if (chunk.error) {
                    logger.error(`流式响应中发现错误块 ${chunkCount}: ${chunk.error}`);

                    // 对于 Gemini 模型，某些错误可能是可恢复的
                    if (isGeminiModel) {
                        // 检查是否是可恢复的错误
                        const isRecoverableError = chunk.error.includes('temporarily unavailable') ||
                                                 chunk.error.includes('rate limit') ||
                                                 chunk.error.includes('overloaded');

                        if (isRecoverableError) {
                            logger.warn(`Gemini 模型遇到可恢复错误，继续处理: ${chunk.error}`);
                            continue;
                        }
                    }

                    // 发送错误响应给客户端
                    const errorChunk = {
                        id: messageId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: streamConfigInfo.modelName,
                        requestId: messageId,
                        chatId: chatid,
                        choices: [{
                            index: 0,
                            delta: {
                                content: chunk.error
                            },
                            finish_reason: 'error'
                        }]
                    };

                    res.write(`data: ${JSON.stringify(errorChunk)}\n\n`);
                    throw new Error(chunk.error);
                }

                // 检查超时（如果块之间间隔太长）
                if (isGemini25 && timeSinceLastChunk > 30000) { // Gemini 2.5 30秒超时
                    logger.warn(`Gemini 2.5 流式响应超时，块间隔: ${timeSinceLastChunk}ms`);
                    throw new Error('Gemini 2.5 流式响应超时');
                } else if (isGeminiModel && timeSinceLastChunk > 20000) { // 其他 Gemini 20秒超时
                    logger.warn(`Gemini 流式响应超时，块间隔: ${timeSinceLastChunk}ms`);
                    throw new Error('Gemini 流式响应超时');
                }
            }

            // 检查是否收到了任何内容
            if (chunkCount === 0) {
                logger.error('未收到任何流式响应块');
                throw new Error('未收到流式响应');
            }

            if (fullResponse.length === 0) {
                logger.warn(`收到 ${chunkCount} 个块但没有内容`);
                // 对于某些模型，这可能是正常的（比如被安全过滤器阻止）
                if (isGeminiModel) {
                    logger.info('Gemini 模型可能因安全过滤器阻止了内容生成');
                }
            }

        } catch (streamError: any) {
            logger.error(`流式响应处理错误 (已处理 ${chunkCount} 块):`, streamError);

            // 确保错误信息传递给客户端
            if (!res.headersSent) {
                const errorResponse = {
                    id: messageId,
                    object: 'chat.completion.chunk',
                    created: Math.floor(Date.now() / 1000),
                    model: streamConfigInfo.modelName,
                    requestId: messageId,
                    chatId: chatid,
                    choices: [{
                        index: 0,
                        delta: {
                            content: streamError.message || '流式响应处理错误'
                        },
                        finish_reason: 'error'
                    }]
                };

                res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
            }

            throw streamError;
        }
        // logger.debug("fullResponse", fullResponse);
        const responseLength = encoding.encode(fullResponse).length;
        logger.debug("responseLength", responseLength);
        // 改进数值计算精度，避免使用 toFixed 然后 parseFloat 的组合
        // 使用 Math.round 保持精度，保留6位小数以避免精度丢失
        const rawResponseConsumption = (responseLength / effectiveModelPrice.count) * effectiveModelPrice.outPrice;
        const responseConsumption = Math.round(rawResponseConsumption * 1000000) / 1000000; // 保留6位小数精度
        const responseUpdatedBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -responseConsumption } },
            { new: true }
        );
        if (!responseUpdatedBalance) {
            throw new Error('internal_server_error');
        }

        const responseBalanceTransaction = new BalanceTransaction({
            userId,
            amount: -responseConsumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.Out
        });
        await responseBalanceTransaction.save();

        if (fullResponse !== '') {
            await ChatMessageModel.create({
                id: messageId || uuid,
                chatId: chatid,
                userId: userId, // 添加用户ID
                createdTime: Date.now(),
                role: Role.ASSISTANT,
                content: fullResponse,
                isComplete: true
            });
        }

        // 记录AI配置使用日志
        try {
            const config = await AIConfigManager.getActiveConfig();
            await AIConfigManager.logUsage({
                configId: config._id.toString(),
                modelId: modelPrice._id.toString(),
                userId: userId,
                requestId: messageId || uuid,
                tokensUsed: tokenLength + responseLength,
                cost: consumption + responseConsumption,
                responseTime: Date.now() - startTime,
                success: true
            });
        } catch (logError) {
            logger.error('记录AI配置使用日志失败:', logError);
        }

        res.end();
    } catch (e) {
        logger.error('=== FINAL ERROR CATCH ===');
        logger.error('Error caught in main try-catch:', e);

        // 详细的错误诊断
        if (e instanceof Error) {
            logger.error(`Error Name: ${e.name}`);
            logger.error(`Error Message: ${e.message}`);
            logger.error(`Error Stack: ${e.stack}`);

            // 检查是否是网络相关错误
            if ((e as any).cause) {
                logger.error(`Error Cause:`, (e as any).cause);
            }
        }

        // 输出当前配置用于诊断（优先使用启用配置/统一管理器信息）
        try {
            const activeConfig = await activeAIConfigManager.getActiveConfig();
            logger.error('=== Current Configuration (Active) ===');
            if (activeConfig && activeConfig.configId && activeConfig.modelId) {
                const cfg: any = activeConfig.configId;
                const mdl: any = activeConfig.modelId;
                logger.error(`Config Name: ${cfg.name}`);
                logger.error(`Provider: ${cfg.provider}`);
                logger.error(`Base URL: ${cfg.baseURL}`);
                logger.error(`Model: ${mdl.modelName} (${mdl.displayName})`);
                logger.error(`IsActive: ${cfg.isActive}`);
            } else {
                logger.error('Active config not set. Falling back to default manager info...');
                const info = await aiClientManager.getDefaultClientWithModel();
                logger.error(`Config Name: ${info.configName}`);
                logger.error(`Model: ${info.modelName}`);
                logger.error(`IsActiveConfig: ${info.isActiveConfig}`);
            }
            logger.error('====================================');
        } catch (confErr) {
            // 回退到环境变量输出
            logger.error('=== Current Configuration (Env Fallback) ===');
            logger.error(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? `${process.env.OPENAI_API_KEY.substring(0, 10)}...` : 'NOT SET'}`);
            logger.error(`OPENAI_BASE_URL: ${process.env.OPENAI_BASE_URL || 'NOT SET'}`);
            logger.error(`NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
            logger.error('===========================================');
        }

        // 根据错误类型和AI厂商提供更友好的错误信息
        let errorMessage = 'internal_server_error';
        let isGeminiError = false;
        let isGemini25Error = false;

        // 检查是否是 Gemini 相关错误
        try {
            const configInfo = await getAIClientWithModel();
            isGeminiError = configInfo.modelName.toLowerCase().includes('gemini');
            isGemini25Error = configInfo.modelName.includes('2.5');
        } catch (configError) {
            logger.warn('无法获取模型信息进行错误分类:', configError);
        }

        if (e instanceof Error) {
            logger.error(`错误分类 - Gemini: ${isGeminiError}, Gemini 2.5: ${isGemini25Error}, 消息: ${e.message}`);

            // 网络连接错误
            if (e.message.includes('ECONNRESET') || e.message.includes('Connection error') ||
                e.message.includes('ENOTFOUND') || e.message.includes('ETIMEDOUT')) {
                if (isGeminiError) {
                    errorMessage = isGemini25Error ?
                        'Gemini 2.5 网络连接不稳定，请稍后重试或切换到 Gemini 1.5' :
                        'Gemini 网络连接不稳定，请检查网络配置';
                } else {
                    errorMessage = '网络连接不稳定，请检查网络或API配置';
                }
                logger.error('网络连接错误 - 可能的原因：');
                logger.error('1. API密钥无效');
                logger.error('2. 网络连接问题');
                logger.error('3. API服务不可用');
                logger.error('4. 防火墙阻止连接');
                if (isGeminiError) {
                    logger.error('5. Gemini API 服务区域限制');
                    logger.error('6. Gemini API 临时维护');
                }
            }
            // API密钥错误
            else if (e.message.includes('API key') || e.message.includes('authentication') ||
                     e.message.includes('unauthorized')) {
                errorMessage = isGeminiError ?
                    'Gemini API密钥配置错误，请联系管理员' :
                    'API配置错误，请联系管理员';
            }
            // 超时错误
            else if (e.message.includes('timeout')) {
                if (isGemini25Error) {
                    errorMessage = 'Gemini 2.5 响应超时，该模型响应较慢，请稍后重试或切换到 Gemini 1.5';
                } else if (isGeminiError) {
                    errorMessage = 'Gemini 请求超时，请稍后重试';
                } else {
                    errorMessage = '请求超时，请稍后重试';
                }
            }
            // 限流错误
            else if (e.message.includes('rate limit') || e.message.includes('429')) {
                if (isGeminiError) {
                    errorMessage = isGemini25Error ?
                        'Gemini 2.5 请求频率过高，请稍后重试或切换到其他模型' :
                        'Gemini 请求频率过高，请稍后重试';
                } else {
                    errorMessage = '请求过于频繁，请稍后重试';
                }
            }
            // 配额不足
            else if (e.message.includes('insufficient_quota') || e.message.includes('quota') ||
                     e.message.includes('billing')) {
                errorMessage = isGeminiError ?
                    'Gemini API配额不足，请联系管理员' :
                    'API配额不足，请联系管理员';
            }
            // Gemini 特有错误
            else if (isGeminiError) {
                if (e.message.includes('model is overloaded') || e.message.includes('overloaded')) {
                    errorMessage = isGemini25Error ?
                        'Gemini 2.5 模型负载过高，请稍后重试或切换到 Gemini 1.5' :
                        'Gemini 模型负载过高，请稍后重试';
                } else if (e.message.includes('safety') || e.message.includes('blocked')) {
                    errorMessage = 'Gemini 安全过滤器阻止了内容生成，请调整输入内容';
                } else if (e.message.includes('model not found') || e.message.includes('not found')) {
                    errorMessage = isGemini25Error ?
                        'Gemini 2.5 模型不可用，请切换到 Gemini 1.5 或联系管理员' :
                        'Gemini 模型不可用，请联系管理员';
                } else if (e.message.includes('service unavailable') || e.message.includes('unavailable')) {
                    errorMessage = isGemini25Error ?
                        'Gemini 2.5 服务暂时不可用，请稍后重试或切换到 Gemini 1.5' :
                        'Gemini 服务暂时不可用，请稍后重试';
                } else if (e.message.includes('invalid request') || e.message.includes('bad request')) {
                    errorMessage = 'Gemini 请求格式错误，请联系管理员';
                } else if (isGemini25Error && e.message.includes('model')) {
                    errorMessage = 'Gemini 2.5 模型错误，请尝试切换到 Gemini 1.5 或稍后重试';
                }
            }
            // 流式响应特有错误
            else if (e.message.includes('流式响应') || e.message.includes('stream')) {
                errorMessage = isGeminiError ?
                    (isGemini25Error ? 'Gemini 2.5 流式响应错误，请稍后重试' : 'Gemini 流式响应错误，请稍后重试') :
                    '流式响应错误，请稍后重试';
            }
        }

        const errorResponse = {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: errorMessage
                    },
                    logprobs: null,
                    finish_reason: 'error'
                }
            ]
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.end();
    }
}

/**
 * 计算图像的token成本
 * @param width - 图像的宽度
 * @param height - 图像的高度
 * @param detail - 图像的详细程度 ('low' 或 'high')
 * @returns 返回图像的token成本
 */
function calculateImageTokens(width: number, height: number, detail: 'low' | 'high'): number {
    // detail: low 模式下，固定成本为 85 tokens
    if (detail === 'low') {
        return 85;
    }

    // 确保图像适应2048x2048的范围
    if (width > 2048 || height > 2048) {
        const scalingFactor = Math.min(2048 / width, 2048 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 确保图像的最短边为768px长
    if (width < 768 || height < 768) {
        const scalingFactor = Math.max(768 / width, 768 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 计算512px正方形的数量
    const numTiles = Math.ceil(width / 512) * Math.ceil(height / 512);

    // detail: high 模式下，成本为170 tokens每个512px正方形，加上85 tokens的固定成本
    return numTiles * 170 + 85;
}

export default chatWithOpenai;
