#!/bin/bash

# 简单的 Gemini 2.5 Flash 测试命令

echo "测试 Gemini 2.5 Flash 聊天功能..."

curl -X POST "http://localhost:3000/api/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-aOxnzCfQjl69VGHV18578aFf7fC74cEcB896EcE8Ed8553E2" \
  -H "chatid: gemini-25-test-$(date +%s)" \
  -H "X-Request-Id: req-$(date +%s)" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好，这是一个 Gemini 2.5 Flash 模型的测试。请回复确认你收到了这条消息。"
      }
    ]
  }' \
  --no-buffer \
  -v
